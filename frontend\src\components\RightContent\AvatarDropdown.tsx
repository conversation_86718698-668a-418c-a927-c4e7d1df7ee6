import {
  LogoutOutlined,
  SettingOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import type { MenuProps } from 'antd';
import { Spin, Avatar, message } from 'antd';
import { createStyles } from 'antd-style';
import React from 'react';
import HeaderDropdown from '../HeaderDropdown';
import { AuthService } from '@/services';

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};



const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },

  };
});

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({
  menu,
  children,
}) => {
  const { styles } = useStyles();
  const { initialState, setInitialState } = useModel('@@initialState');

  const onMenuClick: MenuProps['onClick'] = async (event) => {
    const { key } = event;

    // 处理菜单项点击
    if (key === 'profile') {
      history.push('/personal-center');
    } else if (key === 'settings') {
      // 跳转到用户设置页面
      history.push('/user/profile');
    } else if (key === 'logout') {
      // 执行注销逻辑
      try {
        await AuthService.logout();

        // 清除 initialState
        if (setInitialState) {
          await setInitialState({
            currentUser: undefined,
            currentTeam: undefined,
          });
        }

        message.success('已成功退出登录');
        history.push('/user/login');
      } catch (error) {
        console.error('注销失败:', error);
        // 即使API调用失败，也要清除本地状态并跳转
        AuthService.clearToken();
        if (setInitialState) {
          await setInitialState({
            currentUser: undefined,
            currentTeam: undefined,
          });
        }
        history.push('/user/login');
      }
    }
  };

  const loading = (
    <span className={styles.action}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.name) {
    return loading;
  }

  // 构建菜单项 - 包含个人信息、设置和注销选项
  const buildMenuItems = () => {
    const items = [];

    if (menu) {
      // 个人信息选项
      items.push({
        key: 'profile',
        icon: <UserOutlined />,
        label: '个人信息',
      });

      // 设置选项
      items.push({
        key: 'settings',
        icon: <SettingOutlined />,
        label: '设置',
      });

      // 分隔线
      items.push({
        type: 'divider' as const,
      });

      // 注销选项
      items.push({
        key: 'logout',
        icon: <LogoutOutlined />,
        label: '退出登录',
        danger: true,
      });
    }

    return items;
  };

  const menuItems = buildMenuItems();

  return (
    <HeaderDropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems,
      }}
    >
      {children || (
        <span className={styles.action}>
          <Avatar size="small" icon={<UserOutlined />} />
          <span style={{ marginLeft: 8 }}>{currentUser.name}</span>
        </span>
      )}
    </HeaderDropdown>
  );
};
